# 🔄 Guia de Migração para Nova Estrutura

## 📋 Checklist de Migração

### 1. **Criar Nova Estrutura de Pastas**
```bash
# Criar pastas principais
mkdir -p assets/css
mkdir -p assets/js/core
mkdir -p assets/js/components
mkdir -p assets/js/pages
mkdir -p assets/js/utils
mkdir -p assets/js/vendor
mkdir -p assets/images/logos
mkdir -p assets/images/icons
mkdir -p assets/images/backgrounds
mkdir -p assets/images/content
mkdir -p assets/images/optimized
mkdir -p docs/setup
mkdir -p docs/features
mkdir -p docs/api
mkdir -p docs/maintenance
mkdir -p pages
```

### 2. **Mover Arquivos CSS**
- [ ] `styles.css` → `assets/css/main.css` ✅ (já criado)
- [ ] Criar `assets/css/components.css`
- [ ] Criar `assets/css/pages.css`
- [ ] Criar `assets/css/responsive.css`

### 3. **Mover Arquivos JavaScript**
- [ ] `script.js` → `assets/js/main.js` ✅ (j<PERSON> criado)
- [ ] `calculator.js` → `assets/js/pages/calculator.js`
- [ ] `faq-interactive.js` → `assets/js/components/faq-interactive.js`
- [ ] `config.js` → `assets/js/core/config.js` ✅ (já criado)
- [ ] Outros arquivos JS para suas respectivas pastas

### 4. **Mover Imagens**
- [ ] `logo.png` → `assets/images/logos/logo.png`
- [ ] Criar favicons em `assets/images/icons/`
- [ ] Organizar outras imagens por categoria

### 5. **Atualizar Referências nos HTMLs**

#### **index.html**
```html
<!-- Antes -->
<link rel="stylesheet" href="styles.css">
<script src="script.js"></script>

<!-- Depois -->
<link rel="stylesheet" href="assets/css/main.css">
<script src="assets/js/main.js"></script>
```

#### **Todas as páginas HTML**
- [ ] Atualizar caminhos CSS
- [ ] Atualizar caminhos JavaScript
- [ ] Atualizar caminhos de imagens
- [ ] Verificar links internos

### 6. **Atualizar Imports CSS**
```css
/* Em assets/css/main.css */
@import url('components.css');
@import url('pages.css');
@import url('responsive.css');
```

### 7. **Configurar Carregamento Modular JS**
```html
<!-- Ordem de carregamento -->
<script src="assets/js/core/config.js"></script>
<script src="assets/js/main.js"></script>
<script src="assets/js/components/faq-interactive.js"></script>
<!-- Outros scripts conforme necessário -->
```

## 🔧 Comandos de Migração

### **Mover Arquivos Existentes**
```bash
# CSS
mv styles.css assets/css/main.css

# JavaScript
mv calculator.js assets/js/pages/calculator.js
mv faq-interactive.js assets/js/components/faq-interactive.js
mv script.js assets/js/main.js

# Imagens
mv logo.png assets/images/logos/logo.png
```

### **Atualizar Referências**
```bash
# Buscar e substituir em todos os HTMLs
# Linux/Mac:
find . -name "*.html" -exec sed -i 's/styles\.css/assets\/css\/main.css/g' {} \;
find . -name "*.html" -exec sed -i 's/script\.js/assets\/js\/main.js/g' {} \;

# Windows (PowerShell):
Get-ChildItem -Filter "*.html" | ForEach-Object {
    (Get-Content $_.FullName) -replace 'styles\.css', 'assets/css/main.css' | Set-Content $_.FullName
}
```

## ✅ Verificações Pós-Migração

### **Testes Funcionais**
- [ ] Página inicial carrega corretamente
- [ ] CSS aplicado corretamente
- [ ] JavaScript funcionando
- [ ] Formulários operacionais
- [ ] Calculadora funcionando
- [ ] FAQ interativo funcionando
- [ ] Links internos funcionando
- [ ] Imagens carregando

### **Testes de Performance**
- [ ] Tempo de carregamento mantido
- [ ] Recursos carregando em ordem correta
- [ ] Sem erros no console
- [ ] Responsividade mantida

### **Testes de Compatibilidade**
- [ ] Chrome/Edge
- [ ] Firefox
- [ ] Safari
- [ ] Mobile browsers

## 🚨 Backup Antes da Migração

```bash
# Criar backup completo
cp -r . ../backup-logyc-$(date +%Y%m%d)

# Ou criar arquivo ZIP
zip -r backup-logyc-$(date +%Y%m%d).zip . -x "*.git*"
```

## 📝 Documentação Pós-Migração

- [ ] Atualizar README.md
- [ ] Atualizar checklist.md
- [ ] Criar CHANGELOG.md
- [ ] Documentar nova estrutura
- [ ] Atualizar guias de instalação

## 🎯 Benefícios Esperados

### **Organização**
- ✅ Estrutura profissional
- ✅ Fácil manutenção
- ✅ Escalabilidade

### **Performance**
- ✅ Carregamento otimizado
- ✅ Cache mais eficiente
- ✅ Compressão melhorada

### **Desenvolvimento**
- ✅ Localização rápida de arquivos
- ✅ Debugging facilitado
- ✅ Colaboração melhorada

---

**⚠️ Importante**: Sempre faça backup antes de iniciar a migração!

**🎉 Resultado**: Projeto com estrutura de nível enterprise, pronto para crescimento e manutenção profissional!
