/**
 * Logyc Contabilidade - Script Principal
 * Arquivo principal que inicializa todos os módulos
 */

// Aguardar carregamento do DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Logyc Contabilidade - Inicializando...');
    
    // Inicializar módulos principais
    initializeCore();
    initializeComponents();
    initializePages();
    initializeAnalytics();
    
    console.log('✅ Logyc Contabilidade - Inicializado com sucesso!');
});

/**
 * Inicializar módulos principais
 */
function initializeCore() {
    // Performance monitoring
    if (typeof initPerformanceMonitoring === 'function') {
        initPerformanceMonitoring();
    }
    
    // Accessibility features
    if (typeof initAccessibility === 'function') {
        initAccessibility();
    }
    
    // PWA features
    if (typeof initPWA === 'function') {
        initPWA();
    }
    
    // Loading states
    if (typeof initLoadingStates === 'function') {
        initLoadingStates();
    }
}

/**
 * Inicializar componentes
 */
function initializeComponents() {
    // Header e navegação
    initNavigation();
    
    // Formulários
    if (typeof initForms === 'function') {
        initForms();
    }
    
    // FAQ interativo
    if (typeof initFAQ === 'function') {
        initFAQ();
    }
    
    // Chat
    if (typeof initChat === 'function') {
        initChat();
    }
    
    // Notifications
    if (typeof initNotifications === 'function') {
        initNotifications();
    }
}

/**
 * Inicializar funcionalidades específicas de páginas
 */
function initializePages() {
    const currentPage = getCurrentPage();
    
    switch(currentPage) {
        case 'home':
            initHomePage();
            break;
        case 'calculator':
            if (typeof initCalculator === 'function') {
                initCalculator();
            }
            break;
        case 'help':
            if (typeof initHelpForm === 'function') {
                initHelpForm();
            }
            break;
        case 'switch':
            if (typeof initSwitchForm === 'function') {
                initSwitchForm();
            }
            break;
        case 'blog':
            if (typeof initBlog === 'function') {
                initBlog();
            }
            break;
        case 'admin':
            if (typeof initAdminDashboard === 'function') {
                initAdminDashboard();
            }
            break;
    }
}

/**
 * Inicializar analytics e tracking
 */
function initializeAnalytics() {
    // Google Analytics
    if (typeof gtag === 'function') {
        gtag('config', TECH_CONFIG.analytics.googleAnalyticsId);
    }
    
    // Advanced Analytics
    if (typeof initAdvancedAnalytics === 'function') {
        initAdvancedAnalytics();
    }
    
    // A/B Testing
    if (typeof initABTesting === 'function') {
        initABTesting();
    }
    
    // Conversion Optimizer
    if (typeof initConversionOptimizer === 'function') {
        initConversionOptimizer();
    }
}

/**
 * Detectar página atual
 */
function getCurrentPage() {
    const path = window.location.pathname;
    const filename = path.split('/').pop().split('.')[0];
    
    if (filename === '' || filename === 'index') return 'home';
    if (filename === 'calculadora') return 'calculator';
    if (filename === 'como-podemos-ajudar') return 'help';
    if (filename === 'troca-contabilidade') return 'switch';
    if (filename === 'blog') return 'blog';
    if (filename === 'admin-dashboard') return 'admin';
    
    return 'other';
}

/**
 * Inicializar navegação
 */
function initNavigation() {
    // Mobile menu toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const mobileMenu = document.querySelector('.mobile-menu');
    
    if (mobileMenuBtn && mobileMenu) {
        mobileMenuBtn.addEventListener('click', function() {
            mobileMenu.classList.toggle('active');
            mobileMenuBtn.classList.toggle('active');
        });
    }
    
    // Smooth scroll para links internos
    document.querySelectorAll('a[href^="#"]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Header scroll effect
    const header = document.querySelector('.header');
    if (header) {
        let lastScrollY = window.scrollY;
        
        window.addEventListener('scroll', function() {
            const currentScrollY = window.scrollY;
            
            if (currentScrollY > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            
            // Hide/show header on scroll
            if (currentScrollY > lastScrollY && currentScrollY > 200) {
                header.classList.add('hidden');
            } else {
                header.classList.remove('hidden');
            }
            
            lastScrollY = currentScrollY;
        });
    }
}

/**
 * Inicializar página inicial
 */
function initHomePage() {
    // Animações de entrada
    observeElements();
    
    // Contador de estatísticas
    initStatsCounter();
    
    // Testimonials
    if (typeof initTestimonials === 'function') {
        initTestimonials();
    }
}

/**
 * Observer para animações de entrada
 */
function observeElements() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });
    
    // Observar elementos com classes de animação
    document.querySelectorAll('.fade-in, .slide-up, .slide-in-left, .slide-in-right, .scale-in, .bounce-in').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Contador animado para estatísticas
 */
function initStatsCounter() {
    const stats = document.querySelectorAll('.stat-number');
    
    stats.forEach(stat => {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        });
        
        observer.observe(stat);
    });
}

/**
 * Animar contador
 */
function animateCounter(element) {
    const target = parseInt(element.textContent.replace(/\D/g, ''));
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        const suffix = element.textContent.replace(/\d/g, '');
        element.textContent = Math.floor(current) + suffix;
    }, 16);
}

/**
 * Utilitários globais
 */
window.LogycUtils = {
    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // Throttle function
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // Format currency
    formatCurrency: function(value) {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value);
    },
    
    // Validate email
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    // Format phone
    formatPhone: function(phone) {
        return phone.replace(/\D/g, '').replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    }
};
