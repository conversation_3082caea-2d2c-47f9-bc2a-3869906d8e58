/**
 * Logyc Contabilidade - Configurações Principais
 * Arquivo de configuração central do projeto
 */

// Configurações da empresa
const COMPANY_CONFIG = {
    name: 'Logyc Contabilidade',
    phone: '+5541987427111',
    email: '<EMAIL>',
    address: 'Curitiba, PR - Brasil',
    website: 'https://logyccontabilidade.com.br',
    
    // Redes sociais
    social: {
        whatsapp: 'https://wa.me/5541987427111',
        instagram: '#',
        facebook: '#',
        linkedin: '#'
    },
    
    // Configurações de negócio
    business: {
        workingHours: '08:00 - 18:00',
        workingDays: 'Segunda a Sexta',
        responseTime: '24 horas',
        coverage: 'Todo o Brasil'
    }
};

// Configurações técnicas
const TECH_CONFIG = {
    // Analytics
    analytics: {
        googleAnalyticsId: 'GA_MEASUREMENT_ID', // Substituir pelo ID real
        facebookPixelId: 'FB_PIXEL_ID', // Substituir pelo ID real
        hotjarId: 'HOTJAR_ID' // Substituir pelo ID real
    },
    
    // APIs
    apis: {
        baseUrl: window.location.origin,
        endpoints: {
            contact: '/api/contact',
            newsletter: '/api/newsletter',
            calculator: '/api/calculator'
        }
    },
    
    // PWA
    pwa: {
        cacheName: 'logyc-v1',
        offlineUrl: '/offline.html'
    },
    
    // Performance
    performance: {
        lazyLoadOffset: 100,
        debounceDelay: 300,
        throttleDelay: 100
    }
};

// Configurações de UI
const UI_CONFIG = {
    // Animações
    animations: {
        duration: 300,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        stagger: 100
    },
    
    // Breakpoints
    breakpoints: {
        mobile: 768,
        tablet: 1024,
        desktop: 1200
    },
    
    // Cores (sincronizado com CSS)
    colors: {
        primary: '#005aec',
        secondary: '#01d800',
        accent: '#ffe206',
        danger: '#fd0e35'
    }
};

// Exportar configurações
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { COMPANY_CONFIG, TECH_CONFIG, UI_CONFIG };
} else {
    window.COMPANY_CONFIG = COMPANY_CONFIG;
    window.TECH_CONFIG = TECH_CONFIG;
    window.UI_CONFIG = UI_CONFIG;
}
