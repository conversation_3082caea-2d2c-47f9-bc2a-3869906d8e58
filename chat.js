// Sistema de Chat Inteligente - Logyc Contabilidade
class ChatSystem {
    constructor() {
        this.isOpen = false;
        this.messages = [];
        this.currentStep = 'greeting';
        this.userData = {};
        this.init();
    }
    
    init() {
        this.createChatInterface();
        this.setupEventListeners();
        this.loadChatHistory();
        this.startWelcomeSequence();
    }
    
    createChatInterface() {
        // Container principal do chat
        this.chatContainer = document.createElement('div');
        this.chatContainer.className = 'chat-container';
        this.chatContainer.innerHTML = `
            <div class="chat-toggle" id="chatToggle">
                <div class="chat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                    </svg>
                </div>
                <div class="chat-notification" id="chatNotification">1</div>
            </div>
            
            <div class="chat-window" id="chatWindow">
                <div class="chat-header">
                    <div class="chat-header-info">
                        <div class="chat-avatar">
                            <img src="logo.png" alt="Logyc">
                        </div>
                        <div class="chat-header-text">
                            <h4>Logyc Contabilidade</h4>
                            <span class="chat-status">Online</span>
                        </div>
                    </div>
                    <button class="chat-close" id="chatClose">×</button>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <!-- Mensagens serão inseridas aqui -->
                </div>
                
                <div class="chat-quick-actions" id="chatQuickActions">
                    <!-- Ações rápidas serão inseridas aqui -->
                </div>
                
                <div class="chat-input-container">
                    <input type="text" class="chat-input" id="chatInput" placeholder="Digite sua mensagem...">
                    <button class="chat-send" id="chatSend">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.chatContainer);
        this.setupStyles();
    }
    
    setupStyles() {
        const styles = `
            .chat-container {
                position: fixed;
                bottom: 20px;
                right: 100px;
                z-index: 999;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            
            .chat-toggle {
                width: 60px;
                height: 60px;
                background: #005aec;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0, 90, 236, 0.4);
                transition: all 0.3s ease;
                position: relative;
            }
            
            .chat-toggle:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 20px rgba(0, 90, 236, 0.6);
            }
            
            .chat-icon {
                color: white;
                transition: transform 0.3s ease;
            }
            
            .chat-toggle.active .chat-icon {
                transform: rotate(180deg);
            }
            
            .chat-notification {
                position: absolute;
                top: -5px;
                right: -5px;
                background: #fd0e35;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                animation: pulse 2s infinite;
            }
            
            .chat-notification.hidden {
                display: none;
            }
            
            .chat-window {
                position: absolute;
                bottom: 70px;
                right: 0;
                width: 350px;
                height: 500px;
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
                display: flex;
                flex-direction: column;
                transform: scale(0) translateY(20px);
                opacity: 0;
                transition: all 0.3s ease;
                overflow: hidden;
            }
            
            .chat-window.open {
                transform: scale(1) translateY(0);
                opacity: 1;
            }
            
            .chat-header {
                background: #005aec;
                color: white;
                padding: 16px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            
            .chat-header-info {
                display: flex;
                align-items: center;
                gap: 12px;
            }
            
            .chat-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                overflow: hidden;
                background: white;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .chat-avatar img {
                width: 30px;
                height: 30px;
                object-fit: contain;
            }
            
            .chat-header-text h4 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }
            
            .chat-status {
                font-size: 12px;
                opacity: 0.9;
            }
            
            .chat-close {
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: background 0.2s;
            }
            
            .chat-close:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            
            .chat-messages {
                flex: 1;
                padding: 16px;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                gap: 12px;
            }
            
            .chat-message {
                max-width: 80%;
                padding: 12px 16px;
                border-radius: 18px;
                font-size: 14px;
                line-height: 1.4;
                animation: messageSlide 0.3s ease;
            }
            
            .chat-message.bot {
                background: #f0f0f0;
                align-self: flex-start;
                border-bottom-left-radius: 6px;
            }
            
            .chat-message.user {
                background: #005aec;
                color: white;
                align-self: flex-end;
                border-bottom-right-radius: 6px;
            }
            
            .chat-message.typing {
                background: #f0f0f0;
                align-self: flex-start;
                border-bottom-left-radius: 6px;
                padding: 16px;
            }
            
            .typing-indicator {
                display: flex;
                gap: 4px;
            }
            
            .typing-dot {
                width: 8px;
                height: 8px;
                background: #999;
                border-radius: 50%;
                animation: typing 1.4s infinite;
            }
            
            .typing-dot:nth-child(2) {
                animation-delay: 0.2s;
            }
            
            .typing-dot:nth-child(3) {
                animation-delay: 0.4s;
            }
            
            .chat-quick-actions {
                padding: 12px 16px;
                border-top: 1px solid #eee;
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
            }
            
            .quick-action {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 20px;
                padding: 8px 16px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s;
                white-space: nowrap;
            }
            
            .quick-action:hover {
                background: #005aec;
                color: white;
                border-color: #005aec;
            }
            
            .chat-input-container {
                padding: 16px;
                border-top: 1px solid #eee;
                display: flex;
                gap: 12px;
                align-items: center;
            }
            
            .chat-input {
                flex: 1;
                border: 1px solid #ddd;
                border-radius: 20px;
                padding: 12px 16px;
                font-size: 14px;
                outline: none;
                transition: border-color 0.2s;
            }
            
            .chat-input:focus {
                border-color: #005aec;
            }
            
            .chat-send {
                background: #005aec;
                border: none;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                color: white;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.2s;
            }
            
            .chat-send:hover {
                background: #0047ba;
            }
            
            .chat-send:disabled {
                background: #ccc;
                cursor: not-allowed;
            }
            
            @keyframes messageSlide {
                from {
                    opacity: 0;
                    transform: translateY(10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes typing {
                0%, 60%, 100% {
                    transform: translateY(0);
                }
                30% {
                    transform: translateY(-10px);
                }
            }
            
            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                }
                50% {
                    transform: scale(1.1);
                }
            }
            
            @media (max-width: 768px) {
                .chat-container {
                    bottom: 15px;
                    right: 85px;
                }

                .chat-window {
                    width: 280px;
                    height: 400px;
                    right: -20px;
                }

                .chat-toggle {
                    width: 50px;
                    height: 50px;
                }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }
    
    setupEventListeners() {
        const toggle = document.getElementById('chatToggle');
        const close = document.getElementById('chatClose');
        const input = document.getElementById('chatInput');
        const send = document.getElementById('chatSend');
        
        toggle.addEventListener('click', () => this.toggleChat());
        close.addEventListener('click', () => this.closeChat());
        send.addEventListener('click', () => this.sendMessage());
        
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
        
        // Auto-resize input
        input.addEventListener('input', () => {
            const sendBtn = document.getElementById('chatSend');
            sendBtn.disabled = !input.value.trim();
        });
    }
    
    toggleChat() {
        const window = document.getElementById('chatWindow');
        const toggle = document.getElementById('chatToggle');
        const notification = document.getElementById('chatNotification');
        
        this.isOpen = !this.isOpen;
        
        if (this.isOpen) {
            window.classList.add('open');
            toggle.classList.add('active');
            notification.classList.add('hidden');
            document.getElementById('chatInput').focus();
        } else {
            window.classList.remove('open');
            toggle.classList.remove('active');
        }
    }
    
    closeChat() {
        this.isOpen = false;
        document.getElementById('chatWindow').classList.remove('open');
        document.getElementById('chatToggle').classList.remove('active');
    }
    
    addMessage(text, isBot = true, delay = 0) {
        setTimeout(() => {
            const messagesContainer = document.getElementById('chatMessages');
            const message = document.createElement('div');
            message.className = `chat-message ${isBot ? 'bot' : 'user'}`;
            message.textContent = text;
            
            messagesContainer.appendChild(message);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            this.messages.push({ text, isBot, timestamp: Date.now() });
            this.saveChatHistory();
        }, delay);
    }
    
    showTyping() {
        const messagesContainer = document.getElementById('chatMessages');
        const typing = document.createElement('div');
        typing.className = 'chat-message typing';
        typing.id = 'typingIndicator';
        typing.innerHTML = `
            <div class="typing-indicator">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;
        
        messagesContainer.appendChild(typing);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    hideTyping() {
        const typing = document.getElementById('typingIndicator');
        if (typing) {
            typing.remove();
        }
    }
    
    sendMessage() {
        const input = document.getElementById('chatInput');
        const text = input.value.trim();
        
        if (!text) return;
        
        this.addMessage(text, false);
        input.value = '';
        document.getElementById('chatSend').disabled = true;
        
        this.processUserMessage(text);
    }
    
    processUserMessage(message) {
        this.showTyping();
        
        setTimeout(() => {
            this.hideTyping();
            const response = this.generateResponse(message);
            this.addMessage(response.text);
            
            if (response.actions) {
                this.showQuickActions(response.actions);
            }
        }, 1500);
    }
    
    generateResponse(message) {
        const lowerMessage = message.toLowerCase();
        
        // Respostas baseadas em palavras-chave
        if (lowerMessage.includes('preço') || lowerMessage.includes('valor') || lowerMessage.includes('custo')) {
            return {
                text: 'Nossos preços variam conforme o tipo de empresa e faturamento. Use nossa calculadora para ter uma estimativa personalizada!',
                actions: ['Ver Calculadora', 'Falar com Especialista']
            };
        }
        
        if (lowerMessage.includes('mei')) {
            return {
                text: 'Para MEI, nossa mensalidade é R$ 75,00 + R$ 40,00 por funcionário. Cuidamos de toda a parte fiscal e contábil!',
                actions: ['Quero Contratar', 'Mais Informações']
            };
        }
        
        if (lowerMessage.includes('abertura') || lowerMessage.includes('abrir empresa')) {
            return {
                text: 'Fazemos abertura de empresas ME, LTDA e MEI. O processo é rápido e cuidamos de toda a documentação!',
                actions: ['Iniciar Abertura', 'Ver Documentos Necessários']
            };
        }
        
        if (lowerMessage.includes('trocar') || lowerMessage.includes('mudar contabilidade')) {
            return {
                text: 'Facilitamos todo o processo de troca! Cuidamos da transferência sem complicações.',
                actions: ['Solicitar Troca', 'Ver Vantagens']
            };
        }
        
        if (lowerMessage.includes('funcionário') || lowerMessage.includes('pessoal')) {
            return {
                text: 'Cuidamos de todo o departamento pessoal: admissões, demissões, folha de pagamento, férias e muito mais!',
                actions: ['Saber Mais', 'Falar com RH']
            };
        }
        
        if (lowerMessage.includes('obrigado') || lowerMessage.includes('valeu')) {
            return {
                text: 'Por nada! Estou aqui para ajudar. Precisa de mais alguma coisa?',
                actions: ['Falar com Humano', 'Encerrar Chat']
            };
        }
        
        // Resposta padrão
        return {
            text: 'Entendi! Posso te ajudar com informações sobre nossos serviços contábeis. O que você gostaria de saber?',
            actions: ['Ver Serviços', 'Calcular Mensalidade', 'Falar com Especialista']
        };
    }
    
    showQuickActions(actions) {
        const container = document.getElementById('chatQuickActions');
        container.innerHTML = '';
        
        actions.forEach(action => {
            const button = document.createElement('button');
            button.className = 'quick-action';
            button.textContent = action;
            button.addEventListener('click', () => this.handleQuickAction(action));
            container.appendChild(button);
        });
    }
    
    handleQuickAction(action) {
        this.addMessage(action, false);
        
        setTimeout(() => {
            this.showTyping();
            
            setTimeout(() => {
                this.hideTyping();
                let response = '';
                
                switch (action) {
                    case 'Ver Calculadora':
                        response = 'Ótimo! Vou te direcionar para nossa calculadora.';
                        setTimeout(() => window.location.href = '/calculadora.html', 1000);
                        break;
                    case 'Falar com Especialista':
                        response = 'Perfeito! Vou te conectar com nosso WhatsApp para falar com um especialista.';
                        setTimeout(() => window.open(window.LogycUtils.getWhatsAppUrl('help'), '_blank'), 1000);
                        break;
                    case 'Quero Contratar':
                        response = 'Excelente! Vamos te direcionar para o WhatsApp para finalizar a contratação.';
                        setTimeout(() => window.open(window.LogycUtils.getWhatsAppUrl('contact'), '_blank'), 1000);
                        break;
                    case 'Solicitar Troca':
                        response = 'Vou te levar para o formulário de troca de contabilidade.';
                        setTimeout(() => window.location.href = '/troca-contabilidade.html', 1000);
                        break;
                    default:
                        response = 'Entendi! Como posso te ajudar mais?';
                }
                
                this.addMessage(response);
            }, 1000);
        }, 500);
    }
    
    startWelcomeSequence() {
        setTimeout(() => {
            this.addMessage('Olá! 👋 Sou o assistente virtual da Logyc Contabilidade!');
            
            setTimeout(() => {
                this.addMessage('Como posso te ajudar hoje?');
                this.showQuickActions(['Ver Serviços', 'Calcular Mensalidade', 'Trocar Contabilidade']);
            }, 2000);
        }, 3000);
    }
    
    saveChatHistory() {
        localStorage.setItem('logycChatHistory', JSON.stringify(this.messages));
    }
    
    loadChatHistory() {
        const history = localStorage.getItem('logycChatHistory');
        if (history) {
            this.messages = JSON.parse(history);
            // Recriar mensagens na interface se necessário
        }
    }
}

// Inicializar chat quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    // Aguardar um pouco para não interferir com outros scripts
    setTimeout(() => {
        window.chatSystem = new ChatSystem();
    }, 2000);
});

// Exportar para uso global
window.ChatSystem = ChatSystem;
