# Guia de Instalação - Logyc Contabilidade

## Pré-requisitos

- Servidor web (Apache, Nginx, ou similar)
- PHP 7.4+ (para processamento de formulários)
- Certificado SSL
- Domínio configurado

## Estrutura de Arquivos

```
logyc-contabilidade/
├── assets/
│   ├── css/
│   ├── js/
│   └── images/
├── pages/
├── docs/
├── index.html
├── manifest.json
├── robots.txt
└── sitemap.xml
```

## Passos de Instalação

### 1. Upload dos Arquivos
- Faça upload de todos os arquivos para o servidor
- Mantenha a estrutura de pastas

### 2. Configurações
- Edite `assets/js/core/config.js` com suas informações
- Configure o número do WhatsApp
- Adicione IDs do Google Analytics

### 3. SSL e Domínio
- Configure certificado SSL
- Aponte o domínio para o servidor
- Teste todas as páginas

### 4. Testes
- Teste todos os formulários
- Verifique responsividade
- Teste performance

## Configurações Opcionais

### Backend para Formulários
- Configure processamento PHP
- Integre com sistema de email
- Configure banco de dados para leads

### Analytics
- Configure Google Analytics
- Configure Facebook Pixel
- Configure Hotjar (opcional)

## Manutenção

- Backup regular dos arquivos
- Monitoramento de performance
- Atualizações de segurança
