/**
 * FAQ Interactive Functionality
 * Logyc Contabilidade - Sistema de FAQ Interativo
 */

// Função para alternar FAQ
function toggleFaq(element) {
    const faqItem = element.closest('.faq-item');
    const faqAnswer = faqItem.querySelector('.faq-answer');
    const isActive = faqItem.classList.contains('active');
    
    // Fechar todos os outros FAQs
    document.querySelectorAll('.faq-item.active').forEach(item => {
        if (item !== faqItem) {
            item.classList.remove('active');
            const answer = item.querySelector('.faq-answer');
            answer.style.maxHeight = '0';
        }
    });
    
    // Alternar o FAQ atual
    if (isActive) {
        faqItem.classList.remove('active');
        faqAnswer.style.maxHeight = '0';
    } else {
        faqItem.classList.add('active');
        faqAnswer.style.maxHeight = faqAnswer.scrollHeight + 'px';
    }
    
    // Adicionar animação suave
    setTimeout(() => {
        if (faqItem.classList.contains('active')) {
            faqAnswer.style.maxHeight = 'none';
        }
    }, 300);
}

// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Adicionar eventos de teclado para acessibilidade
    document.querySelectorAll('.faq-question').forEach(question => {
        question.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleFaq(this);
            }
        });
        
        // Tornar focável
        question.setAttribute('tabindex', '0');
        question.setAttribute('role', 'button');
        question.setAttribute('aria-expanded', 'false');
    });
    
    // Observador para animações quando elementos entram na viewport
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationDelay = `${Array.from(entry.target.parentNode.children).indexOf(entry.target) * 0.1}s`;
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observar todos os itens do FAQ
    document.querySelectorAll('.faq-item').forEach(item => {
        observer.observe(item);
    });
});

// Função para buscar no FAQ
function searchFaq(searchTerm) {
    const faqItems = document.querySelectorAll('.faq-item');
    const term = searchTerm.toLowerCase();
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question h3').textContent.toLowerCase();
        const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
        
        if (question.includes(term) || answer.includes(term)) {
            item.style.display = 'block';
            
            // Destacar termo encontrado
            if (term && term.length > 2) {
                highlightSearchTerm(item, term);
            }
        } else {
            item.style.display = 'none';
        }
    });
}

// Função para destacar termos de busca
function highlightSearchTerm(element, term) {
    const walker = document.createTreeWalker(
        element,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );
    
    const textNodes = [];
    let node;
    
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }
    
    textNodes.forEach(textNode => {
        const parent = textNode.parentNode;
        if (parent.tagName !== 'SCRIPT' && parent.tagName !== 'STYLE') {
            const text = textNode.textContent;
            const regex = new RegExp(`(${term})`, 'gi');
            
            if (regex.test(text)) {
                const highlightedText = text.replace(regex, '<mark class="search-highlight">$1</mark>');
                const wrapper = document.createElement('span');
                wrapper.innerHTML = highlightedText;
                parent.replaceChild(wrapper, textNode);
            }
        }
    });
}

// Função para limpar destaques de busca
function clearSearchHighlights() {
    document.querySelectorAll('.search-highlight').forEach(highlight => {
        const parent = highlight.parentNode;
        parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
        parent.normalize();
    });
}

// Analytics para FAQ
function trackFaqInteraction(question) {
    if (typeof gtag !== 'undefined') {
        gtag('event', 'faq_interaction', {
            'event_category': 'FAQ',
            'event_label': question,
            'value': 1
        });
    }
    
    // Analytics customizado
    if (window.logycAnalytics) {
        window.logycAnalytics.track('FAQ Opened', {
            question: question,
            timestamp: new Date().toISOString()
        });
    }
}

// Exportar funções para uso global
window.toggleFaq = toggleFaq;
window.searchFaq = searchFaq;
window.clearSearchHighlights = clearSearchHighlights;
