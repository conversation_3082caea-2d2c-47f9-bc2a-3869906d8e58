# Logyc Contabilidade - Configurações Apache
# Otimizações de performance, segurança e SEO

# ==========================================
# COMPRESSÃO GZIP
# ==========================================
<IfModule mod_deflate.c>
    # Comprimir HTML, CSS, JavaScript, Text, XML e fontes
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# ==========================================
# CACHE DO NAVEGADOR
# ==========================================
<IfModule mod_expires.c>
    ExpiresActive on

    # Imagens
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"

    # CSS e JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"

    # Fontes
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"

    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# ==========================================
# HEADERS DE CACHE
# ==========================================
<IfModule mod_headers.c>
    # Cache para recursos estáticos
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|otf)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>

    # Cache para HTML
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>

    # Headers de segurança
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# ==========================================
# REWRITE RULES
# ==========================================
<IfModule mod_rewrite.c>
    RewriteEngine On

    # Forçar HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

    # Remover www (opcional - ajustar conforme necessário)
    RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

    # URLs amigáveis
    RewriteRule ^como-podemos-ajudar/?$ como-podemos-ajudar.html [L]
    RewriteRule ^troca-contabilidade/?$ troca-contabilidade.html [L]
    RewriteRule ^calculadora/?$ calculadora.html [L]
    RewriteRule ^blog/?$ blog.html [L]

    # Página 404 personalizada
    ErrorDocument 404 /404.html
</IfModule>

# ==========================================
# SEGURANÇA
# ==========================================

# Bloquear acesso a arquivos sensíveis
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acesso a pastas de sistema
RedirectMatch 404 /\.git
RedirectMatch 404 /\.svn
RedirectMatch 404 /node_modules

# Prevenir hotlinking de imagens
RewriteEngine on
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?logyccontabilidade\.com\.br [NC]
RewriteRule \.(jpg|jpeg|png|gif|svg)$ - [NC,F,L]

# ==========================================
# OTIMIZAÇÕES ADICIONAIS
# ==========================================

# Remover assinatura do servidor
ServerTokens Prod

# Desabilitar listagem de diretórios
Options -Indexes

# Charset UTF-8
AddDefaultCharset UTF-8

# MIME Types para fontes web
AddType application/font-woff2 .woff2
AddType application/font-woff .woff
AddType application/vnd.ms-fontobject .eot
AddType font/ttf .ttf
AddType font/otf .otf

# ==========================================
# PWA SUPPORT
# ==========================================

# Service Worker
<Files "sw.js">
    Header set Cache-Control "no-cache"
</Files>

# Manifest
<Files "manifest.json">
    Header set Content-Type "application/manifest+json"
</Files>

# ==========================================
# REDIRECTS ESPECÍFICOS
# ==========================================

# Redirect de páginas antigas (se houver)
# Redirect 301 /old-page.html /new-page.html

# ==========================================
# LOGS PERSONALIZADOS (opcional)
# ==========================================

# LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\"" combined
# CustomLog logs/access.log combined
