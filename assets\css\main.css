/* 
 * Logyc Contabilidade - CSS Principal
 * Arquivo principal com variáveis, reset e estilos globais
 */

/* Importar estilos específicos */
@import url('components.css');
@import url('pages.css');
@import url('responsive.css');

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* Reset CSS moderno */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--gray-900);
    background: var(--white);
    overflow-x: hidden;
}

/* Paleta de cores moderna - Baseada na identidade visual Logyc */
:root {
    /* Cores principais da identidade visual */
    --primary-blue: #005aec;
    --primary-blue-dark: #004bb8;
    --primary-blue-light: #0593ff;
    --secondary-green: #01d800;
    --secondary-green-dark: #217345;
    --accent-yellow: #ffe206;
    --accent-red: #fd0e35;

    /* Cores neutras modernas */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Gradientes modernos */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-green) 0%, var(--secondary-green-dark) 100%);
    --gradient-hero: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 30%, var(--secondary-green-dark) 70%, var(--secondary-green) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-yellow) 0%, #ffed4e 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* Sombras aprimoradas */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-colored: 0 10px 25px -5px rgba(0, 90, 236, 0.3);
    --shadow-green: 0 10px 25px -5px rgba(1, 216, 0, 0.3);

    /* Transições suaves */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Bordas modernas */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    --radius-full: 9999px;

    /* Espaçamentos */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Compatibilidade com cores antigas */
    --verde-claro: var(--secondary-green);
    --verde-escuro: var(--secondary-green-dark);
    --vermelho: var(--accent-red);
    --amarelo: var(--accent-yellow);
    --azul-claro: var(--primary-blue-light);
    --azul-escuro: var(--primary-blue-dark);
    --branco: var(--white);
    --cinza-claro: var(--gray-50);
    --cinza-escuro: var(--gray-700);
}

/* Estilos globais */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
}

/* Utilitários de texto */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Utilitários de display */
.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

/* Utilitários de flexbox */
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

/* Utilitários de espaçamento */
.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mt-1 { margin-top: var(--space-sm); }
.mb-1 { margin-bottom: var(--space-sm); }
.pt-1 { padding-top: var(--space-sm); }
.pb-1 { padding-bottom: var(--space-sm); }

/* Acessibilidade */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus states para acessibilidade */
*:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Smooth scrolling para navegação */
html {
    scroll-behavior: smooth;
}

/* Prevenção de overflow horizontal */
body {
    overflow-x: hidden;
}

/* Otimização de performance */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Seleção de texto personalizada */
::selection {
    background: var(--primary-blue);
    color: var(--white);
}

::-moz-selection {
    background: var(--primary-blue);
    color: var(--white);
}
